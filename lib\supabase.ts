import { createClient } from '@supabase/supabase-js';
import { CorporateFiling, FilingsResponse, FilterState, SortOption } from './types';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Fetch corporate filings with filters and pagination
export async function fetchCorporateFilings(
  page: number = 1,
  pageSize: number = 20,
  filters?: FilterState,
  sort?: SortOption
): Promise<FilingsResponse> {
  try {
    let query = supabase
      .from('master_corporate_announcements')
      .select('*', { count: 'exact' })
      .not('summary', 'is', null);

    // Apply filters
    if (filters) {
      if (filters.search) {
        query = query.or(`company_name.ilike.%${filters.search}%,headline.ilike.%${filters.search}%`);
      }
      
      if (filters.company) {
        query = query.ilike('company_name', `%${filters.company}%`);
      }
      
      if (filters.subcategory) {
        query = query.eq('subcategory', filters.subcategory);
      }
      
      if (filters.dateRange.start) {
        query = query.gte('created_at', filters.dateRange.start);
      }
      
      if (filters.dateRange.end) {
        query = query.lte('created_at', filters.dateRange.end);
      }
    }

    // Apply sorting
    if (sort) {
      query = query.order(sort.field, { ascending: sort.direction === 'asc' });
    } else {
      // Default sort by broadcast_date_time descending, fallback to created_at
      query = query.order('broadcast_date_time', { ascending: false, nullsFirst: false })
                   .order('created_at', { ascending: false });
    }

    // Apply pagination
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching corporate filings:', error);
      return { data: [], count: 0, error: error.message };
    }

    return { data: data as CorporateFiling[], count: count || 0 };
  } catch (error) {
    console.error('Unexpected error:', error);
    return { data: [], count: 0, error: 'An unexpected error occurred' };
  }
}

// Fetch unique subcategories for filter dropdown
export async function fetchSubcategories(): Promise<string[]> {
  try {
    const { data, error } = await supabase
      .from('master_corporate_announcements')
      .select('subcategory')
      .not('subcategory', 'is', null)
      .not('summary', 'is', null);

    if (error) {
      console.error('Error fetching subcategories:', error);
      return [];
    }

    // Get unique subcategories
    const uniqueSubcategories = [...new Set(data.map(item => item.subcategory))];
    return uniqueSubcategories.filter(Boolean).sort();
  } catch (error) {
    console.error('Unexpected error:', error);
    return [];
  }
}

// Fetch unique company names for filter dropdown
export async function fetchCompanyNames(): Promise<string[]> {
  try {
    const { data, error } = await supabase
      .from('master_corporate_announcements')
      .select('company_name')
      .not('company_name', 'is', null)
      .not('summary', 'is', null);

    if (error) {
      console.error('Error fetching company names:', error);
      return [];
    }

    // Get unique company names
    const uniqueCompanies = [...new Set(data.map(item => item.company_name))];
    return uniqueCompanies.filter(Boolean).sort();
  } catch (error) {
    console.error('Unexpected error:', error);
    return [];
  }
}

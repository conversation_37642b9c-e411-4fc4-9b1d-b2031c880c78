#!/usr/bin/env python3
"""
Debug script to check what data we're getting from the database
"""

import json
from ai_document_analyzer import DocumentA<PERSON>yzer, DEFAULT_CONFIG

def debug_unprocessed_record():
    """Debug the unprocessed record to see what data we have"""
    
    print("🔍 Debugging Unprocessed Record")
    print("=" * 50)
    
    # Initialize analyzer
    try:
        config = DEFAULT_CONFIG.copy()
        config['log_level'] = 'DEBUG'
        analyzer = DocumentAnalyzer(config)
        print("✅ Analyzer initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize analyzer: {e}")
        return
    
    if not analyzer.supabase:
        print("❌ Supabase client not available")
        return
    
    try:
        # Get the unprocessed record
        result = analyzer.supabase.table('master_corporate_announcements').select('*').is_('summary', None).limit(1).execute()
        
        if not result.data:
            print("ℹ️  No unprocessed records found")
            return
        
        record = result.data[0]
        print(f"📄 Found unprocessed record: {record.get('id', 'unknown')}")
        print()
        
        # Print all available fields
        print("📋 Record fields:")
        for key, value in record.items():
            if isinstance(value, str) and len(value) > 100:
                print(f"   {key}: {value[:100]}...")
            else:
                print(f"   {key}: {value}")
        print()
        
        # Test classification
        headline = record.get('headline', '')
        details = record.get('details', '')
        
        print("🔍 Testing classification...")
        print(f"   Headline: {headline[:100] if headline else 'None'}...")
        print(f"   Details: {details[:100] if details else 'None'}...")
        print()
        
        doc_type, classification = analyzer.classify_document_type(
            details,
            headline,
            details,
            record.get('id')
        )
        
        print(f"📊 Classification result:")
        print(f"   Type: {doc_type}")
        print(f"   Confidence: {classification.confidence_score:.3f}")
        print(f"   Method: {classification.classification_method}")
        print(f"   Keywords: {classification.matched_keywords}")
        print(f"   Fallback: {classification.fallback_used}")
        print()
        
        # Test AI analysis
        if analyzer.openai_client:
            print("🤖 Testing AI analysis...")
            try:
                analysis = analyzer.analyze_document_with_ai(
                    details,
                    headline,
                    details,
                    doc_type
                )
                
                if analysis:
                    print("✅ AI analysis successful!")
                    print(f"   Generated headline: {analysis.get('headline', 'N/A')}")
                    print(f"   Summary: {analysis.get('summary', 'N/A')[:100]}...")
                    print(f"   Confidence: {analysis.get('confidence_score', 0):.3f}")
                else:
                    print("❌ AI analysis returned None")
                    
            except Exception as e:
                print(f"❌ AI analysis failed: {e}")
                import traceback
                print(f"Full traceback: {traceback.format_exc()}")
        else:
            print("⚠️  OpenAI client not available")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    debug_unprocessed_record()

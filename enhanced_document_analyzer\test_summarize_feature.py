#!/usr/bin/env python3
"""
Test script for the new summarize feature
"""

import json
import os
from ai_document_analyzer import Document<PERSON>naly<PERSON>, DEFAULT_CONFIG

def test_summarize_feature():
    """Test the new summarize feature"""
    
    print("🧪 Testing Summarize Feature")
    print("=" * 50)
    
    # Initialize analyzer
    try:
        config = DEFAULT_CONFIG.copy()
        config['log_level'] = 'INFO'
        analyzer = DocumentAnalyzer(config)
        print("✅ Analyzer initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize analyzer: {e}")
        return False
    
    if not analyzer.supabase:
        print("❌ Supabase client not available")
        return False
    
    try:
        # Get a sample record to test with
        result = analyzer.supabase.table('master_corporate_announcements').select('id').limit(1).execute()
        
        if not result.data:
            print("❌ No records found in database")
            return False
        
        test_record_id = result.data[0]['id']
        print(f"📄 Using test record ID: {test_record_id}")
        
        # Test the summarize feature
        print("\n🔍 Testing summarize_record_to_json method...")
        output_path = f"test_summary_{test_record_id}.json"
        
        summary_result = analyzer.summarize_record_to_json(test_record_id, output_path)
        
        if "error" in summary_result:
            print(f"❌ Summarization failed: {summary_result['error']}")
            return False
        
        # Check if file was created
        if os.path.exists(output_path):
            print(f"✅ JSON file created successfully: {output_path}")
            
            # Load and display summary info
            with open(output_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print("\n📋 Summary Information:")
            print(f"   Record ID: {data.get('record_id', 'N/A')}")
            print(f"   Company: {data.get('original_data', {}).get('company_name', 'N/A')}")
            print(f"   Date: {data.get('original_data', {}).get('broadcast_date_time', 'N/A')}")
            print(f"   Document Type: {data.get('classification', {}).get('document_type', 'N/A')}")
            print(f"   Confidence: {data.get('classification', {}).get('confidence_score', 'N/A')}")
            print(f"   AI Headline: {data.get('ai_analysis', {}).get('headline', 'N/A')}")
            print(f"   File Size: {os.path.getsize(output_path)} bytes")
            
            # Clean up test file
            try:
                os.remove(output_path)
                print(f"🧹 Cleaned up test file: {output_path}")
            except:
                print(f"⚠️  Could not clean up test file: {output_path}")
            
            return True
        else:
            print(f"❌ JSON file was not created: {output_path}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False

def show_usage_examples():
    """Show usage examples for the new feature"""
    print("\n📚 Usage Examples:")
    print("=" * 50)
    print()
    print("1. Summarize a specific record (auto-generated filename):")
    print("   python ai_document_analyzer.py --summarize YOUR_RECORD_ID")
    print()
    print("2. Summarize with custom output path:")
    print("   python ai_document_analyzer.py --summarize YOUR_RECORD_ID --output my_summary.json")
    print()
    print("3. Use in Python code:")
    print("   from ai_document_analyzer import DocumentAnalyzer, DEFAULT_CONFIG")
    print("   analyzer = DocumentAnalyzer(DEFAULT_CONFIG)")
    print("   result = analyzer.summarize_record_to_json('your_record_id')")
    print()
    print("4. Test with this script:")
    print("   python test_summarize_feature.py")

if __name__ == "__main__":
    print("🚀 Enhanced Document Analyzer - Summarize Feature Test")
    print("=" * 60)
    
    # Run the test
    success = test_summarize_feature()
    
    # Show usage examples
    show_usage_examples()
    
    if success:
        print("\n🎉 Test completed successfully!")
        print("✅ The summarize feature is working correctly.")
    else:
        print("\n❌ Test failed!")
        print("Please check the error messages above.")
    
    print("\n" + "=" * 60)

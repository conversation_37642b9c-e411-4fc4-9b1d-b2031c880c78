'use client';

import { useState } from 'react';
import { ArrowUpDown, ChevronDown, Calendar, Building } from 'lucide-react';
import { SortOption } from '@/lib/types';

interface SortDropdownProps {
  sort: SortOption;
  onSortChange: (sort: SortOption) => void;
}

const sortOptions = [
  { field: 'broadcast_date_time' as const, direction: 'desc' as const, label: 'Latest Broadcast', icon: Calendar },
  { field: 'broadcast_date_time' as const, direction: 'asc' as const, label: 'Earliest Broadcast', icon: Calendar },
  { field: 'created_at' as const, direction: 'desc' as const, label: 'Newest First', icon: Calendar },
  { field: 'created_at' as const, direction: 'asc' as const, label: 'Oldest First', icon: Calendar },
  { field: 'company_name' as const, direction: 'asc' as const, label: 'Company A-Z', icon: Building },
  { field: 'company_name' as const, direction: 'desc' as const, label: 'Company Z-A', icon: Building },
];

export default function SortDropdown({ sort, onSortChange }: SortDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);

  const currentOption = sortOptions.find(
    option => option.field === sort.field && option.direction === sort.direction
  ) || sortOptions[0];

  const handleSortSelect = (option: typeof sortOptions[0]) => {
    onSortChange({
      field: option.field,
      direction: option.direction,
    });
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-4 py-2 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors text-sm"
      >
        <ArrowUpDown className="w-4 h-4 text-slate-500" />
        <span className="text-slate-700">{currentOption.label}</span>
        <ChevronDown 
          className={`w-4 h-4 text-slate-500 transition-transform ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown Content */}
          <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl z-20 py-1 border border-slate-200">
            {sortOptions.map((option) => {
              const Icon = option.icon;
              const isSelected = option.field === sort.field && option.direction === sort.direction;
              
              return (
                <button
                  key={`${option.field}-${option.direction}`}
                  onClick={() => handleSortSelect(option)}
                  className={`w-full flex items-center gap-3 px-4 py-2 text-sm transition-colors ${
                    isSelected 
                      ? 'bg-slate-100 text-[#2c374b] font-medium' 
                      : 'text-slate-700 hover:bg-slate-50'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {option.label}
                </button>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
}

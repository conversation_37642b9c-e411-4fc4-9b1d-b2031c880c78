<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StrikeDeck - Feed</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts: Poppins -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Ionicons for icons -->
    <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
    <!-- AlpineJS for dropdown interactivity -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        // Customizing Tailwind CSS with the new color scheme
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'strikedeck-blue': {
                            'light': '#f1f5f9', // slate-100 for active link background
                            'DEFAULT': '#2c374b', // The brand color
                            'dark': '#3b4a61' // A slightly lighter shade for hover
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* Use Poppins as the default font */
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc; /* A neutral, light background */
        }
        /* Custom scrollbar for a more refined look */
        ::-webkit-scrollbar {
            width: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        ::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }
        /* Style for the active category link */
        .nav-link.active {
            background-color: #f1f5f9; /* strikedeck-blue-light (slate-100) */
            color: #2c374b; /* strikedeck-blue */
            font-weight: 600;
        }
        .main-content {
            height: 100vh;
            overflow-y: auto;
        }
    </style>
</head>
<body class="text-slate-700">

    <div class="grid grid-cols-12 min-h-screen bg-white">
        <!-- Left Sidebar -->
        <aside class="col-span-2 border-r border-slate-200 p-6 flex flex-col justify-between">
            <div>
                <div class="flex items-center gap-3 mb-10">
                    <div class="w-10 h-10 bg-strikedeck-blue rounded-lg flex items-center justify-center text-white shadow-md">
                         <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                           <circle cx="12" cy="12" r="7.5" />
                           <path stroke-linecap="round" d="M12 2v4m0 12v4m-8-8H2m20 0h-4" />
                         </svg>
                    </div>
                    <h1 class="font-bold text-xl text-slate-800">StrikeDeck</h1>
                </div>

                <h2 class="text-xs font-semibold text-slate-400 uppercase tracking-wider mb-3">Menu</h2>
                <nav class="space-y-2">
                    <a href="#" class="nav-link active flex items-center gap-3 p-2.5 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
                        <ion-icon name="newspaper-outline" class="text-xl"></ion-icon>
                        <span>Feed</span>
                    </a>
                    <a href="#" class="nav-link flex items-center gap-3 p-2.5 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
                        <ion-icon name="star-outline" class="text-xl"></ion-icon>
                        <span>Watchlist</span>
                    </a>
                    <a href="#" class="nav-link flex items-center gap-3 p-2.5 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
                        <ion-icon name="briefcase-outline" class="text-xl"></ion-icon>
                        <span>Portfolio</span>
                    </a>
                    <a href="#" class="nav-link flex items-center gap-3 p-2.5 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
                        <ion-icon name="calendar-outline" class="text-xl"></ion-icon>
                        <span>Events Calendar</span>
                    </a>
                </nav>
            </div>
            <div class="space-y-2">
                 <a href="#" class="nav-link flex items-center gap-3 p-2.5 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
                    <ion-icon name="settings-outline" class="text-xl"></ion-icon>
                    <span>Settings</span>
                </a>
                 <a href="#" class="nav-link flex items-center gap-3 p-2.5 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
                    <ion-icon name="help-circle-outline" class="text-xl"></ion-icon>
                    <span>Help</span>
                </a>
            </div>
        </aside>

        <!-- Main Content Area -->
        <div class="col-span-10 bg-slate-50 main-content">
            <header class="sticky top-0 bg-slate-50/80 backdrop-blur-sm z-10 flex justify-between items-center p-8 border-b border-slate-200">
                <div class="relative w-full max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <ion-icon name="search" class="text-slate-400"></ion-icon>
                    </div>
                    <input type="text" placeholder="Search..." class="w-full bg-white border border-slate-300 rounded-lg py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-slate-400 transition">
                </div>
                <div x-data="{ open: false }" class="relative">
                    <button @click="open = !open" class="flex items-center gap-3 text-slate-600 hover:text-slate-800 transition-colors">
                        <span class="font-semibold text-sm hidden sm:inline">John Doe</span>
                        <ion-icon name="person-circle-outline" class="text-3xl"></ion-icon>
                        <ion-icon name="chevron-down-outline" class="text-sm transition-transform" :class="{'rotate-180': open}"></ion-icon>
                    </button>
                    <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl z-20 py-1 border border-slate-200">
                        <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">My Account</a>
                        <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Settings</a>
                        <hr class="border-slate-200 my-1">
                        <a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50">Logout</a>
                    </div>
                </div>
            </header>

            <!-- Tab Navigation -->
            <div class="px-8 pt-4 border-b border-slate-200" x-data="{ activeView: 'all_companies', selectedWatchlist: 'Tech Stocks', watchlistOpen: false }">
                <nav class="flex space-x-2">
                    <button @click="activeView = 'all_companies'" :class="{'border-strikedeck-blue text-strikedeck-blue': activeView === 'all_companies', 'border-transparent text-slate-500 hover:text-slate-700': activeView !== 'all_companies'}" class="px-3 py-3 font-semibold text-sm border-b-2 transition-colors">
                        All Companies
                    </button>
                    <button @click="activeView = 'my_companies'" :class="{'border-strikedeck-blue text-strikedeck-blue': activeView === 'my_companies', 'border-transparent text-slate-500 hover:text-slate-700': activeView !== 'my_companies'}" class="px-3 py-3 font-semibold text-sm border-b-2 transition-colors">
                        All My Companies
                    </button>
                    <button @click="activeView = 'portfolio'" :class="{'border-strikedeck-blue text-strikedeck-blue': activeView === 'portfolio', 'border-transparent text-slate-500 hover:text-slate-700': activeView !== 'portfolio'}" class="px-3 py-3 font-semibold text-sm border-b-2 transition-colors">
                        My Portfolio
                    </button>
                    <div class="relative" @click.away="watchlistOpen = false">
                        <button @click="watchlistOpen = !watchlistOpen; activeView = 'watchlist'" :class="{'border-strikedeck-blue text-strikedeck-blue': activeView === 'watchlist', 'border-transparent text-slate-500 hover:text-slate-700': activeView !== 'watchlist'}" class="flex items-center gap-2 px-3 py-3 font-semibold text-sm border-b-2 transition-colors">
                            <span x-text="selectedWatchlist"></span>
                            <ion-icon name="chevron-down-outline" class="text-xs transition-transform" :class="{'rotate-180': watchlistOpen}"></ion-icon>
                        </button>
                        <div x-show="watchlistOpen" x-transition class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-xl z-20 py-1 border border-slate-200">
                            <a href="#" @click="selectedWatchlist = 'Tech Stocks'; watchlistOpen = false" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Tech Stocks</a>
                            <a href="#" @click="selectedWatchlist = 'FMCG Watch'; watchlistOpen = false" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">FMCG Watch</a>
                            <a href="#" @click="selectedWatchlist = 'New Energy'; watchlistOpen = false" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">New Energy</a>
                        </div>
                    </div>
                </nav>
            </div>


            <div class="grid grid-cols-3 gap-8 p-8">
                <!-- Feed Column -->
                <div class="col-span-2 space-y-6">
                    <!-- Card 1: Results -->
                    <article class="bg-white border border-slate-200 rounded-xl shadow-sm p-6">
                        <div class="flex items-start justify-between">
                            <div>
                                <h3 class="font-semibold text-lg text-strikedeck-blue transition-colors hover:text-strikedeck-blue-dark hover:underline cursor-pointer">Reliance Industries Ltd.</h3>
                                <p class="text-xs text-slate-500">July 17, 2025</p>
                            </div>
                            <div class="flex items-center gap-3">
                                <span class="text-xs font-semibold text-blue-700 bg-blue-100 px-2 py-1 rounded-full">Results</span>
                                <a href="#" class="text-sm font-semibold text-slate-600 hover:underline">View Post</a>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-slate-100">
                            <h2 class="text-lg font-bold text-slate-900">Q1 FY26 Results</h2>
                            <p class="mt-2 text-sm text-slate-600">Strong growth driven by robust performance in the O2C segment and continued momentum in Retail and Digital Services. Margins improved due to operational efficiencies. Capex for the new energy business is on track.</p>
                            <div class="mt-3 space-y-1 text-sm text-slate-700 bg-slate-50 p-4 rounded-lg">
                                <p><strong>Revenue:</strong> 2,40,532 Cr <span class="text-green-600 font-semibold">(↑ 12% YoY)</span></p>
                                <p><strong>Net Profit:</strong> 19,876 Cr <span class="text-green-600 font-semibold">(↑ 18% YoY)</span></p>
                            </div>
                        </div>
                    </article>
                    
                    <!-- Card 2: M&A -->
                    <article class="bg-white border border-slate-200 rounded-xl shadow-sm p-6">
                          <div class="flex items-start justify-between">
                            <div>
                                <h3 class="font-semibold text-lg text-strikedeck-blue transition-colors hover:text-strikedeck-blue-dark hover:underline cursor-pointer">HDFC Bank Ltd.</h3>
                                <p class="text-xs text-slate-500">July 16, 2025</p>
                            </div>
                            <div class="flex items-center gap-3">
                                <span class="text-xs font-semibold text-teal-700 bg-teal-100 px-2 py-1 rounded-full">M&A</span>
                                <a href="#" class="text-sm font-semibold text-slate-600 hover:underline">View Post</a>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-slate-100">
                             <h2 class="text-lg font-bold text-slate-900">Acquisition of 'PaySwift'</h2>
                            <p class="mt-2 text-sm text-slate-600">HDFC Bank to acquire digital payments startup PaySwift Technologies in an all-cash deal valued at 1,200 Cr. The acquisition will bolster the bank's digital payment ecosystem and merchant network. Not a related party transaction.</p>
                        </div>
                    </article>
                </div>

                <!-- Right Column -->
                <div class="col-span-1 space-y-8">
                    <div>
                        <h2 class="text-lg font-bold text-strikedeck-blue mb-4">Upcoming Events</h2>
                        <div class="space-y-4">
                            <div class="flex items-center gap-4 p-3 rounded-lg hover:bg-white transition-colors">
                                <div class="w-10 h-10 bg-slate-200 text-slate-600 rounded-lg flex flex-col items-center justify-center font-bold">
                                    <p class="text-xs -mb-0.5">JUL</p>
                                    <p class="text-lg">21</p>
                                </div>
                                <div>
                                    <p class="font-semibold text-sm text-slate-700">TCS Earnings Call</p>
                                    <p class="text-xs text-slate-500">Q1 FY26 Results</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-4 p-3 rounded-lg hover:bg-white transition-colors">
                                <div class="w-10 h-10 bg-slate-200 text-slate-600 rounded-lg flex flex-col items-center justify-center font-bold">
                                    <p class="text-xs -mb-0.5">JUL</p>
                                    <p class="text-lg">24</p>
                                </div>
                                <div>
                                    <p class="font-semibold text-sm text-slate-700">Infosys AGM</p>
                                    <p class="text-xs text-slate-500">Annual General Meeting</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-strikedeck-blue mb-4">Pinned Announcements</h2>
                        <div class="space-y-3">
                            <a href="#" class="block p-4 rounded-lg hover:bg-white transition-colors border-l-4 border-slate-300">
                                <div class="flex items-center gap-2">
                                    <ion-icon name="pin" class="text-slate-500"></ion-icon>
                                    <span class="text-xs font-semibold text-emerald-800 bg-emerald-100 px-2 py-0.5 rounded-full">Annual Report</span>
                                </div>
                                <p class="mt-2 text-sm font-semibold text-slate-700">Infosys Ltd. - Annual Report FY25</p>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

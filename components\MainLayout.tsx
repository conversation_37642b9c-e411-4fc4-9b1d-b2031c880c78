'use client';

import { useState, createContext, useContext } from 'react';
import Sidebar from './Sidebar';
import Header from './Header';
import TabNavigation from './TabNavigation';
import { FilterState, SortOption } from '@/lib/types';

interface MainLayoutProps {
  children: React.ReactNode;
}

interface AppContextType {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  activeView: string;
  setActiveView: (view: string) => void;
  searchValue: string;
  setSearchValue: (value: string) => void;
  filters: FilterState;
  setFilters: (filters: FilterState) => void;
  sort: SortOption;
  setSort: (sort: SortOption) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within MainLayout');
  }
  return context;
};

export default function MainLayout({ children }: MainLayoutProps) {
  const [activeTab, setActiveTab] = useState('feed');
  const [activeView, setActiveView] = useState('all_companies');
  const [searchValue, setSearchValue] = useState('');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    company: '',
    filingType: '',
    dateRange: { start: '', end: '' },
    subcategory: '',
  });
  const [sort, setSort] = useState<SortOption>({
    field: 'broadcast_date_time',
    direction: 'desc',
  });

  const contextValue: AppContextType = {
    activeTab,
    setActiveTab,
    activeView,
    setActiveView,
    searchValue,
    setSearchValue,
    filters,
    setFilters,
    sort,
    setSort,
  };

  return (
    <AppContext.Provider value={contextValue}>
      <div className="flex min-h-screen bg-white">
        {/* Mobile Sidebar Overlay */}
        {isSidebarOpen && (
          <div
            className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
            onClick={() => setIsSidebarOpen(false)}
          />
        )}

        {/* Left Sidebar */}
        <div className={`
          fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0 lg:w-auto lg:flex-shrink-0
          ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        `}>
          <Sidebar
            activeTab={activeTab}
            onTabChange={setActiveTab}
            onClose={() => setIsSidebarOpen(false)}
          />
        </div>

        {/* Main Content Area */}
        <div className="flex-1 bg-slate-50 main-content lg:ml-0">
          {/* Header */}
          <Header
            searchValue={searchValue}
            onSearchChange={setSearchValue}
            onMenuClick={() => setIsSidebarOpen(true)}
          />

          {/* Tab Navigation */}
          <TabNavigation
            activeView={activeView}
            onViewChange={setActiveView}
          />

          {/* Main Content */}
          <div className="p-4 sm:p-6 lg:p-8">
            {children}
          </div>
        </div>
      </div>
    </AppContext.Provider>
  );
}

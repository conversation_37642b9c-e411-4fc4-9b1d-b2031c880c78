// Corporate Filing Types
export interface CorporateFiling {
  id: string;
  pdf_hash: string;
  source_exchange: string;
  has_nse_data?: boolean;
  has_bse_data?: boolean;
  created_at?: string;
  updated_at?: string;
  broadcast_date_time?: string;
  nse_source_id?: number;
  bse_source_id?: string;
  company_name?: string;
  nse_symbol?: string;
  bse_code?: string;
  headline?: string;
  details?: string;
  subcategory?: string;
  classification?: string;
  attachmentfiles?: string;
  xmlfiles?: string;
  size?: number;
  summary?: FilingSummary;
}

// Filing Summary JSON Structure (Enhanced v2.0)
export interface FilingSummary {
  type: string;
  headline?: string;
  summary: string;
  filing_date: string;
  filing_type: string;
  processed_at: string;
  key_disclosures: KeyDisclosures;
  regulatory_body: string;
  confidence_score: number;
  processor_version?: string;
  compliance_requirement: string;
  // Enhanced features v2.0
  tags?: string[];
  key_events?: KeyEvents;
  sentiment?: 'positive' | 'negative' | 'neutral';
  impact?: 'high' | 'medium' | 'low';
  impact_reasoning?: string;
}

// Key Events Structure
export interface KeyEvents {
  primary_event?: string;
  event_date?: string;
  other_important_dates?: string[];
}

export interface KeyDisclosures {
  event?: string;
  agenda?: string;
  period_end?: string;
  meeting_date?: string;
  period?: string;
  document?: string;
  key_metrics?: {
    total_employees?: number;
    export_contribution?: string;
    manufacturing_locations?: {
      national?: number;
      international?: number;
    };
    revenue_from_manufacturing?: string;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

// UI State Types
export interface FilterState {
  search: string;
  company: string;
  filingType: string;
  dateRange: {
    start: string;
    end: string;
  };
  subcategory: string;
}

export interface SortOption {
  field: 'created_at' | 'company_name' | 'filing_date' | 'broadcast_date_time';
  direction: 'asc' | 'desc';
}

// API Response Types
export interface FilingsResponse {
  data: CorporateFiling[];
  count: number;
  error?: string;
}

// Component Props Types
export interface FilingCardProps {
  filing: CorporateFiling;
}

export interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export interface FilterPanelProps {
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
}

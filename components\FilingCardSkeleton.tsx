export default function FilingCardSkeleton() {
  return (
    <article className="bg-white border border-slate-200 rounded-xl shadow-sm p-6 animate-pulse">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="h-6 bg-slate-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-slate-200 rounded w-1/4 mb-1"></div>
          <div className="h-3 bg-slate-200 rounded w-1/3"></div>
        </div>
        <div className="flex items-center gap-3">
          <div className="h-6 bg-slate-200 rounded-full w-16"></div>
          <div className="h-4 bg-slate-200 rounded w-16"></div>
        </div>
      </div>

      {/* Content */}
      <div className="mt-4 pt-4 border-t border-slate-100">
        {/* Title */}
        <div className="h-6 bg-slate-200 rounded w-2/3 mb-3"></div>
        
        {/* Summary */}
        <div className="space-y-2 mb-4">
          <div className="h-4 bg-slate-200 rounded w-full"></div>
          <div className="h-4 bg-slate-200 rounded w-5/6"></div>
          <div className="h-4 bg-slate-200 rounded w-3/4"></div>
        </div>

        {/* Metrics Box */}
        <div className="bg-slate-50 p-4 rounded-lg space-y-2">
          <div className="h-4 bg-slate-200 rounded w-1/2"></div>
          <div className="h-4 bg-slate-200 rounded w-2/3"></div>
        </div>

        {/* Footer */}
        <div className="mt-4 flex gap-4">
          <div className="h-3 bg-slate-200 rounded w-20"></div>
          <div className="h-3 bg-slate-200 rounded w-16"></div>
          <div className="h-3 bg-slate-200 rounded w-12"></div>
        </div>
      </div>
    </article>
  );
}

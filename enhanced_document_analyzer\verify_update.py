#!/usr/bin/env python3
"""
Verify that the record was updated with the generated headline
"""

import json
from ai_document_analyzer import DocumentAnaly<PERSON>, DEFAULT_CONFIG

def verify_update():
    """Verify the record was updated correctly"""
    
    print("🔍 Verifying Record Update")
    print("=" * 40)
    
    # Initialize analyzer
    try:
        analyzer = DocumentAnalyzer(DEFAULT_CONFIG)
        print("✅ Analyzer initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize analyzer: {e}")
        return
    
    if not analyzer.supabase:
        print("❌ Supabase client not available")
        return
    
    try:
        # Get the record that was just processed
        result = analyzer.supabase.table('master_corporate_announcements').select('*').eq('id', 'ea3cb3b8-8eed-484f-9f1a-7156108f4a5c').execute()
        
        if not result.data:
            print("❌ Record not found")
            return
        
        record = result.data[0]
        print(f"📄 Record ID: {record.get('id')}")
        print(f"🏢 Company: {record.get('company_name')}")
        print(f"📰 Original headline: {record.get('headline', 'N/A')}")
        print()
        
        # Check if summary was updated
        summary = record.get('summary')
        if summary:
            print("✅ Summary found!")
            if isinstance(summary, dict):
                print(f"🎯 Generated headline: {summary.get('headline', 'N/A')}")
                print(f"📝 Summary: {summary.get('summary', 'N/A')[:100]}...")
                print(f"🔍 Document type: {summary.get('type', 'N/A')}")
                print(f"📊 Confidence: {summary.get('confidence_score', 0):.3f}")
                print(f"⏰ Processed at: {summary.get('processed_at', 'N/A')}")
                
                # Check if headline is different from original
                original_headline = record.get('headline', '')
                generated_headline = summary.get('headline', '')
                
                if generated_headline and generated_headline != original_headline:
                    print(f"\n🎉 SUCCESS! Generated headline is different from original:")
                    print(f"   Original:  {original_headline}")
                    print(f"   Generated: {generated_headline}")
                else:
                    print(f"\n⚠️  Generated headline is same as original or missing")
            else:
                print(f"⚠️  Summary is not a dictionary: {type(summary)}")
        else:
            print("❌ No summary found in record")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    verify_update()

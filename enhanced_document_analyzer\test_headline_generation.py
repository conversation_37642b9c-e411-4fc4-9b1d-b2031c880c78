#!/usr/bin/env python3
"""
Test script for headline generation functionality

This script tests the enhanced document analyzer's ability to generate
self-explanatory headlines for corporate filings.
"""

import json
import sys
from ai_document_analyzer import DocumentAnalyzer, DEFAULT_CONFIG

def test_headline_generation():
    """Test headline generation with sample data"""
    
    print("🧪 Testing Enhanced Document Analyzer - Headline Generation")
    print("=" * 60)
    
    # Initialize analyzer
    try:
        analyzer = DocumentAnalyzer(DEFAULT_CONFIG)
        print("✅ Analyzer initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize analyzer: {e}")
        return False
    
    # Test cases
    test_cases = [
        {
            "name": "Earnings Call",
            "headline": "Reliance Industries Limited - Investor/Analyst Call",
            "details": "Reliance Industries Limited has scheduled an investor/analyst call to discuss Q3 FY2024 financial results. The call will cover revenue growth, EBITDA margins, and future outlook.",
            "content": "Reliance Industries reported strong Q3 results with 15% revenue growth and improved margins across all business segments.",
            "expected_type": "earnings_call"
        },
        {
            "name": "Financial Results",
            "details": "TCS announces Q2 FY2024 results with revenue of ₹59,162 crores, up 8.7% YoY. Net income increased to ₹11,342 crores. Strong performance in North America and Europe markets.",
            "headline": "Tata Consultancy Services - Q2 FY2024 Results",
            "content": "TCS delivered strong financial performance in Q2 with revenue growth and margin expansion.",
            "expected_type": "financial_results"
        },
        {
            "name": "Merger & Acquisition",
            "headline": "HDFC Bank - Merger with HDFC Ltd",
            "details": "HDFC Bank announces completion of merger with HDFC Ltd, creating India's largest private sector bank. The merger is valued at ₹1.2 lakh crores.",
            "content": "The merger between HDFC Bank and HDFC Ltd has been completed, creating a financial services giant.",
            "expected_type": "merger_acquisition"
        }
    ]
    
    print(f"\n📋 Running {len(test_cases)} test cases...\n")
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test Case {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            # Classify document type
            doc_type, classification = analyzer.classify_document_type(
                test_case['content'],
                test_case['headline'],
                test_case['details']
            )
            
            print(f"📝 Original headline: {test_case['headline']}")
            print(f"🔍 Classified as: {doc_type} (confidence: {classification.confidence_score:.2f})")
            
            # Generate AI analysis with headline
            if analyzer.openai_client:
                analysis = analyzer.analyze_document_with_ai(
                    test_case['content'],
                    test_case['headline'],
                    test_case['details'],
                    doc_type
                )
                
                if analysis and 'headline' in analysis:
                    print(f"✨ Generated headline: {analysis['headline']}")
                    print(f"📄 Summary: {analysis.get('summary', 'N/A')[:100]}...")
                    print(f"🎯 Confidence: {analysis.get('confidence_score', 0):.2f}")
                    success_count += 1
                    print("✅ Test passed")
                else:
                    print("❌ Failed to generate headline")
            else:
                print("⚠️  OpenAI client not available, skipping AI analysis")
                print("✅ Classification test passed")
                success_count += 1
            
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
        
        print()
    
    print("=" * 60)
    print(f"📊 Test Results: {success_count}/{len(test_cases)} tests passed")
    
    if success_count == len(test_cases):
        print("🎉 All tests passed successfully!")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False

def test_document_registry():
    """Test document type registry functionality"""
    
    print("\n🔧 Testing Document Type Registry")
    print("-" * 40)
    
    try:
        analyzer = DocumentAnalyzer(DEFAULT_CONFIG)
        registry = analyzer.document_registry
        
        # Test registry loading
        doc_types = registry.list_document_types()
        print(f"📚 Loaded {len(doc_types)} document types:")
        for doc_type in doc_types[:5]:  # Show first 5
            print(f"   - {doc_type}")
        
        if len(doc_types) > 5:
            print(f"   ... and {len(doc_types) - 5} more")
        
        # Test template access
        if 'earnings_call' in registry.document_types:
            template = registry.document_types['earnings_call'].template
            has_headline = 'headline' in template
            print(f"✅ Earnings call template {'includes' if has_headline else 'missing'} headline field")
        
        print("✅ Document registry test passed")
        return True
        
    except Exception as e:
        print(f"❌ Document registry test failed: {e}")
        return False

def main():
    """Main test function"""
    
    print("🚀 Enhanced Document Analyzer - Test Suite")
    print("=" * 60)
    
    # Test 1: Document Registry
    registry_success = test_document_registry()
    
    # Test 2: Headline Generation
    headline_success = test_headline_generation()
    
    # Overall results
    print("\n" + "=" * 60)
    print("📋 OVERALL TEST RESULTS")
    print("=" * 60)
    
    if registry_success and headline_success:
        print("🎉 ALL TESTS PASSED!")
        print("\n✅ The enhanced document analyzer is working correctly.")
        print("✅ Headlines will be generated for new document analysis.")
        print("✅ The card component will display generated headlines.")
        sys.exit(0)
    else:
        print("❌ SOME TESTS FAILED!")
        print("\n⚠️  Please check the error messages above.")
        print("⚠️  Ensure environment variables are set correctly:")
        print("   - OPENAI_API_KEY or OPENROUTER_API_KEY")
        print("   - SUPABASE_KEY")
        sys.exit(1)

if __name__ == "__main__":
    main()

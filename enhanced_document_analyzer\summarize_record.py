#!/usr/bin/env python3
"""
Simple script to summarize a specific record and save to JSON
"""

import sys
import argparse
from ai_document_analyzer import DocumentAnalyzer, DEFAULT_CONFIG

def main():
    """Main function for summarizing records"""
    parser = argparse.ArgumentParser(
        description="Summarize a specific corporate filing record and save to JSON",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python summarize_record.py 12345
  python summarize_record.py 12345 --output my_analysis.json
  python summarize_record.py 12345 --verbose
        """
    )
    
    parser.add_argument('record_id', help='The ID of the record to summarize')
    parser.add_argument('--output', '-o', help='Output JSON file path (default: summary_{record_id}.json)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Configure logging level
    config = DEFAULT_CONFIG.copy()
    if args.verbose:
        config['log_level'] = 'DEBUG'
    else:
        config['log_level'] = 'INFO'
    
    print(f"🔍 Summarizing record: {args.record_id}")
    if args.output:
        print(f"📁 Output file: {args.output}")
    else:
        print(f"📁 Output file: summary_{args.record_id}.json")
    
    try:
        # Initialize analyzer
        analyzer = DocumentAnalyzer(config)
        
        # Summarize the record
        result = analyzer.summarize_record_to_json(args.record_id, args.output)
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
            return 1
        
        # Display results
        output_file = args.output or f"summary_{args.record_id}.json"
        print(f"\n✅ Successfully created summary: {output_file}")
        print("\n📋 Summary Details:")
        print(f"   Record ID: {result['record_id']}")
        print(f"   Company: {result['original_data'].get('company_name', 'N/A')}")
        print(f"   Date: {result['original_data'].get('broadcast_date_time', 'N/A')}")
        print(f"   Exchange: {result['original_data'].get('exchange', 'N/A')}")
        print(f"   Symbol: {result['original_data'].get('symbol', 'N/A')}")
        print(f"   Document Type: {result['classification']['document_type']}")
        print(f"   Confidence Score: {result['classification']['confidence_score']:.3f}")
        print(f"   AI Generated Headline: {result['ai_analysis'].get('headline', 'N/A')}")
        
        # Show key disclosures if available
        key_disclosures = result['ai_analysis'].get('key_disclosures', {})
        if key_disclosures:
            print(f"   Main Event: {key_disclosures.get('event', 'N/A')}")
            print(f"   Key Points: {key_disclosures.get('key_points', 'N/A')}")
        
        print(f"   Processing Time: {result['processing_metadata']['processed_at']}")
        print(f"   Model Used: {result['processing_metadata']['model_used']}")
        
        print(f"\n📄 The complete analysis has been saved to: {output_file}")
        print("   You can open this file to see the full detailed analysis.")
        
        return 0
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        if args.verbose:
            import traceback
            print(f"Full traceback: {traceback.format_exc()}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

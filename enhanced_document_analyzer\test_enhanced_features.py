#!/usr/bin/env python3
"""
Test script for the enhanced summarize features (v2.0)
Tests: Symbol matching, tags, sentiment, impact, key events, etc.
"""

import json
import os
from ai_document_analyzer import DocumentAnalyzer, DEFAULT_CONFIG

def test_enhanced_features():
    """Test all the new enhanced features"""
    
    print("🚀 Testing Enhanced Summarize Features (v2.0)")
    print("=" * 60)
    
    # Initialize analyzer
    try:
        config = DEFAULT_CONFIG.copy()
        config['log_level'] = 'INFO'
        analyzer = DocumentAnalyzer(config)
        print("✅ Analyzer initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize analyzer: {e}")
        return False
    
    if not analyzer.supabase:
        print("❌ Supabase client not available")
        return False
    
    try:
        # Get a few sample records to test with
        result = analyzer.supabase.table('master_corporate_announcements').select('id, company_name, headline').limit(3).execute()
        
        if not result.data:
            print("❌ No records found in database")
            return False
        
        print(f"\n📄 Testing with {len(result.data)} sample records:")
        for i, record in enumerate(result.data, 1):
            print(f"   {i}. {record.get('company_name', 'Unknown')} - {record.get('headline', 'No headline')[:50]}...")
        
        # Test each record
        all_tests_passed = True
        for i, record in enumerate(result.data, 1):
            record_id = record['id']
            company_name = record.get('company_name', 'Unknown')
            
            print(f"\n🔍 Testing Record {i}: {company_name}")
            print("-" * 50)
            
            output_path = f"test_enhanced_{i}_{record_id[:8]}.json"
            
            # Generate summary
            summary_result = analyzer.summarize_record_to_json(record_id, output_path)
            
            if "error" in summary_result:
                print(f"❌ Summarization failed: {summary_result['error']}")
                all_tests_passed = False
                continue
            
            # Test the enhanced features
            test_passed = test_enhanced_json_structure(output_path, company_name)
            if not test_passed:
                all_tests_passed = False
            
            # Clean up test file
            try:
                os.remove(output_path)
                print(f"🧹 Cleaned up: {output_path}")
            except:
                pass
        
        return all_tests_passed
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False

def test_enhanced_json_structure(file_path, company_name):
    """Test the JSON structure for enhanced features"""
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ JSON file loaded: {os.path.getsize(file_path)} bytes")
        
        # Test 1: Enhanced symbols and exchange
        symbols = data.get('symbols_and_exchange', {})
        nse_symbol = symbols.get('nse_symbol')
        bse_code = symbols.get('bse_code')
        source_exchange = symbols.get('source_exchange')
        
        if nse_symbol or bse_code:
            print(f"✅ Symbol matching: NSE={nse_symbol}, BSE={bse_code}, Exchange={source_exchange}")
        else:
            print(f"⚠️  No symbols found (may be normal for some records)")
        
        # Test 2: AI Analysis enhancements
        ai_analysis = data.get('ai_analysis', {})
        
        # Test headline (should be short and without company name)
        headline = ai_analysis.get('headline', '')
        if headline:
            if len(headline) <= 60:
                print(f"✅ Headline length: {len(headline)} chars - '{headline}'")
            else:
                print(f"⚠️  Headline too long: {len(headline)} chars - '{headline}'")
            
            if company_name.split()[0].lower() not in headline.lower():
                print(f"✅ Headline without company name: '{headline}'")
            else:
                print(f"⚠️  Headline contains company name: '{headline}'")
        
        # Test tags
        tags = ai_analysis.get('tags', [])
        if tags and isinstance(tags, list):
            print(f"✅ Tags: {', '.join(tags)}")
        else:
            print(f"⚠️  No tags found")
        
        # Test sentiment
        sentiment = ai_analysis.get('sentiment', '')
        if sentiment in ['positive', 'negative', 'neutral']:
            print(f"✅ Sentiment: {sentiment}")
        else:
            print(f"⚠️  Invalid sentiment: {sentiment}")
        
        # Test impact
        impact = ai_analysis.get('impact', '')
        impact_reasoning = ai_analysis.get('impact_reasoning', '')
        if impact in ['high', 'medium', 'low']:
            print(f"✅ Impact: {impact} - {impact_reasoning[:50]}...")
        else:
            print(f"⚠️  Invalid impact: {impact}")
        
        # Test key events
        key_events = ai_analysis.get('key_events', {})
        primary_event = key_events.get('primary_event', '')
        event_date = key_events.get('event_date')
        if primary_event:
            print(f"✅ Key event: {primary_event} on {event_date or 'no date'}")
        else:
            print(f"⚠️  No key events found")
        
        # Test processing metadata
        metadata = data.get('processing_metadata', {})
        version = metadata.get('analyzer_version', '')
        features = metadata.get('features', [])
        if version == 'enhanced_v2.0' and features:
            print(f"✅ Enhanced version: {version} with {len(features)} features")
        else:
            print(f"⚠️  Version/features issue: {version}, {len(features)} features")
        
        print("✅ All enhanced features tested successfully!")
        return True
        
    except Exception as e:
        print(f"❌ JSON structure test failed: {e}")
        return False

def show_enhanced_features_summary():
    """Show summary of enhanced features"""
    print("\n📋 Enhanced Features Summary (v2.0):")
    print("=" * 60)
    print("🔗 Symbol Matching:")
    print("   • Extracts NSE symbols and BSE codes")
    print("   • Shows source exchange and data availability")
    print()
    print("🏷️  Intelligent Tags:")
    print("   • Results, Legal, Fundraising, Board Changes, etc.")
    print("   • 2-3 most relevant tags per document")
    print()
    print("📰 Enhanced Headlines:")
    print("   • Max 60 characters, action-focused")
    print("   • No company names, just the key event")
    print()
    print("😊 Sentiment Analysis:")
    print("   • Positive, Negative, or Neutral")
    print("   • Based on business impact and news tone")
    print()
    print("📊 Impact Assessment:")
    print("   • High, Medium, or Low business impact")
    print("   • Includes reasoning for the assessment")
    print()
    print("📅 Key Events Extraction:")
    print("   • Primary event identification")
    print("   • Important dates extraction")
    print("   • Timeline of significant events")

if __name__ == "__main__":
    print("🧪 Enhanced Document Analyzer - Feature Test Suite")
    print("=" * 70)
    
    # Run the comprehensive test
    success = test_enhanced_features()
    
    # Show features summary
    show_enhanced_features_summary()
    
    if success:
        print("\n🎉 All enhanced features are working correctly!")
        print("✅ The v2.0 enhancements are ready for use.")
    else:
        print("\n❌ Some tests failed!")
        print("Please check the error messages above.")
    
    print("\n" + "=" * 70)

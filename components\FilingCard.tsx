'use client';

import React from 'react';
import { format } from 'date-fns';
import { ExternalLink, TrendingUp, TrendingDown } from 'lucide-react';
import { CorporateFiling } from '@/lib/types';

interface FilingCardProps {
  filing: CorporateFiling;
}

// Helper function to get badge color and icon based on subcategory
function getBadgeInfo(subcategory: string): { color: string; priority: 'high' | 'medium' | 'low' } {
  const category = subcategory?.toLowerCase() || '';

  if (category.includes('result') || category.includes('financial')) {
    return { color: 'text-blue-700 bg-blue-100 border-blue-200', priority: 'high' };
  }
  if (category.includes('m&a') || category.includes('acquisition') || category.includes('merger')) {
    return { color: 'text-teal-700 bg-teal-100 border-teal-200', priority: 'high' };
  }
  if (category.includes('board') || category.includes('meeting')) {
    return { color: 'text-purple-700 bg-purple-100 border-purple-200', priority: 'medium' };
  }
  if (category.includes('dividend') || category.includes('bonus')) {
    return { color: 'text-green-700 bg-green-100 border-green-200', priority: 'high' };
  }
  if (category.includes('compliance') || category.includes('regulatory')) {
    return { color: 'text-orange-700 bg-orange-100 border-orange-200', priority: 'medium' };
  }
  if (category.includes('annual') || category.includes('report')) {
    return { color: 'text-emerald-700 bg-emerald-100 border-emerald-200', priority: 'medium' };
  }
  if (category.includes('agm') || category.includes('general meeting')) {
    return { color: 'text-indigo-700 bg-indigo-100 border-indigo-200', priority: 'medium' };
  }

  return { color: 'text-slate-700 bg-slate-100 border-slate-200', priority: 'low' };
}

// Helper function to format percentage changes
function formatPercentage(value: unknown): React.JSX.Element | null {
  if (!value || typeof value !== 'string') return null;

  const match = value.match(/([↑↓])\s*(\d+(?:\.\d+)?)/);
  if (!match) return null;

  const [, direction, percentage] = match;
  const isPositive = direction === '↑';

  return (
    <span className={`font-semibold flex items-center gap-1 ${
      isPositive ? 'text-green-600' : 'text-red-600'
    }`}>
      {isPositive ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
      {percentage}%
    </span>
  );
}

// Helper function to render key metrics
function renderKeyMetrics(keyDisclosures: unknown): React.JSX.Element | null {
  if (!keyDisclosures || typeof keyDisclosures !== 'object') return null;

  const keyDisclosuresObj = keyDisclosures as { key_metrics?: Record<string, unknown> };
  const metrics = keyDisclosuresObj.key_metrics;
  if (!metrics) return null;

  return (
    <div className="mt-3 space-y-1 text-sm text-slate-700 bg-slate-50 p-4 rounded-lg">
      {Object.entries(metrics).map(([key, value]) => {
        if (typeof value === 'object') return null;

        const formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        const formattedValue = typeof value === 'string' && (value.includes('↑') || value.includes('↓'))
          ? formatPercentage(value)
          : <span>{String(value)}</span>;

        return (
          <p key={key}>
            <strong>{formattedKey}:</strong> {formattedValue}
          </p>
        );
      })}
    </div>
  );
}

export default function FilingCard({ filing }: FilingCardProps) {
  // Use broadcast_date_time if available, fallback to created_at
  const displayDate = filing.broadcast_date_time || filing.created_at;
  const formattedDate = displayDate
    ? format(new Date(displayDate), 'MMMM dd, yyyy')
    : 'Date not available';

  const formattedBroadcastTime = filing.broadcast_date_time
    ? format(new Date(filing.broadcast_date_time), 'MMM dd, yyyy HH:mm')
    : null;

  const summary = filing.summary;
  const badgeInfo = getBadgeInfo(filing.subcategory || '');

  // Calculate time ago based on broadcast_date_time or created_at
  const timeAgo = displayDate
    ? (() => {
        const now = new Date();
        const broadcastTime = new Date(displayDate);
        const diffInHours = Math.floor((now.getTime() - broadcastTime.getTime()) / (1000 * 60 * 60));

        if (diffInHours < 1) return 'Just now';
        if (diffInHours < 24) return `${diffInHours}h ago`;
        const diffInDays = Math.floor(diffInHours / 24);
        if (diffInDays < 7) return `${diffInDays}d ago`;
        const diffInWeeks = Math.floor(diffInDays / 7);
        return `${diffInWeeks}w ago`;
      })()
    : null;

  // Handle View Post click - redirect to PDF
  const handleViewPost = () => {
    if (filing.attachmentfiles) {
      window.open(filing.attachmentfiles, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <article className="bg-white border border-slate-200 rounded-xl shadow-sm p-4 sm:p-6 hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-3">
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-base sm:text-lg text-[#2c374b] transition-colors hover:text-[#3b4a61] hover:underline cursor-pointer break-words">
            {filing.company_name || 'Company Name Not Available'}
          </h3>
          <div className="flex flex-col gap-1 mt-1">
            <div className="flex items-center gap-2">
              <p className="text-xs text-slate-500">{formattedDate}</p>
              {timeAgo && (
                <>
                  <span className="text-xs text-slate-300">•</span>
                  <p className="text-xs text-slate-500">{timeAgo}</p>
                </>
              )}
            </div>
            {formattedBroadcastTime && (
              <div className="flex items-center gap-1">
                <span className="text-xs text-slate-400">Broadcast:</span>
                <p className="text-xs text-slate-600 font-medium">{formattedBroadcastTime}</p>
              </div>
            )}
          </div>
          <div className="flex flex-wrap gap-2 mt-1">
            {filing.nse_symbol && (
              <span className="text-xs text-slate-600 bg-slate-100 px-2 py-0.5 rounded">
                NSE: {filing.nse_symbol}
              </span>
            )}
            {filing.bse_code && (
              <span className="text-xs text-slate-600 bg-slate-100 px-2 py-0.5 rounded">
                BSE: {filing.bse_code}
              </span>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2 sm:gap-3 flex-shrink-0">
          {filing.subcategory && (
            <div className="flex items-center gap-1">
              {badgeInfo.priority === 'high' && (
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              )}
              <span className={`text-xs font-semibold px-2 py-1 rounded-full border ${badgeInfo.color} whitespace-nowrap`}>
                {filing.subcategory}
              </span>
            </div>
          )}
          <button
            onClick={handleViewPost}
            disabled={!filing.attachmentfiles}
            className={`text-xs sm:text-sm font-semibold flex items-center gap-1 whitespace-nowrap transition-colors ${
              filing.attachmentfiles
                ? 'text-[#2c374b] hover:text-[#3b4a61] hover:underline cursor-pointer'
                : 'text-slate-400 cursor-not-allowed'
            }`}
          >
            <span className="hidden sm:inline">View Post</span>
            <ExternalLink className="w-3 h-3" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="mt-4 pt-4 border-t border-slate-100">
        {/* Headline */}
        {(summary?.headline || filing.headline) && (
          <h2 className="text-base sm:text-lg font-bold text-slate-900 mb-2 break-words">
            {summary?.headline || filing.headline}
          </h2>
        )}

        {/* Summary */}
        {summary?.summary && (
          <p className="mt-2 text-sm text-slate-600 leading-relaxed break-words">
            {summary.summary}
          </p>
        )}

        {/* Key Metrics */}
        {summary?.key_disclosures && renderKeyMetrics(summary.key_disclosures)}

        {/* Additional Info */}
        {summary?.confidence_score && (
          <div className="mt-4 flex items-center gap-1 text-xs text-slate-500">
            <span>Confidence:</span>
            <div className="flex items-center gap-1">
              <div className="w-12 h-1.5 bg-slate-200 rounded-full overflow-hidden">
                <div
                  className={`h-full rounded-full transition-all ${
                    summary.confidence_score >= 0.8 ? 'bg-green-500' :
                    summary.confidence_score >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${summary.confidence_score * 100}%` }}
                />
              </div>
              <span className="font-medium">
                {Math.round(summary.confidence_score * 100)}%
              </span>
            </div>
          </div>
        )}
      </div>
    </article>
  );
}
